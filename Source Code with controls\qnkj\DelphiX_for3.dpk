package DelphiX_for3;

{$R *.RES}
{$R 'DelphiX.dcr'}
{$ALIGN ON}
{$ASSERTIONS OFF}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS OFF}
{$LOCALSYMBOLS OFF}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $00400000}
{$DESCRIPTION 'DelphiX - DirectX component collection for Delphi'}
{$IMPLICITBUILD ON}

requires
  vcl30,
  VC<PERSON>MP30;

contains
  DIB,
  DXClass,
  DXConsts,
  DXDraws,
  DXETable,
  DXInput,
  DXPlay,
  DXPlayFm,
  DXR<PERSON>,
  DXReg,
  DXSounds,
  DXSprite,
  Wave,
  DXFFBEdit,
  DXGUIDEdit,
  DXInptEdit,
  DXPictEdit,
  DXWaveEdit,
  DAnim,
  DirectX,
  DShow;

end.
