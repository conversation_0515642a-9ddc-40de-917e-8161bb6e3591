package dclNMF50;

{$R *.RES}
{$R 'NMReg.dcr'}
{$ALIGN ON}
{ ASSERTIONS ON}
{$BOOLEVAL OFF}
{ DEB<PERSON><PERSON>NFO ON}
{$EXTENDEDSYNTAX ON}
{$IMPOR<PERSON>DDA<PERSON> ON}
{$IOCHECKS ON}
{ LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $5052FFF0}
{$DESCRIPTION 'NetMasters Fastnet Tools'}
{$DESIGNONLY}
{$IMPLICITBUILD OFF}

requires
  vcl50;

contains
  NMReg in 'NMReg.pas';

end.
