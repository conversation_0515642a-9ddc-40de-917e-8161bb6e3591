package dclNMF70;

{$R *.res}
{$R 'NMReg.dcr'}
{$ALIGN 8}
{ ASSERTIONS ON}
{$BOOLEVAL OFF}
{ DEB<PERSON><PERSON>NFO ON}
{$EXTENDEDSYNTAX ON}
{$IMPOR<PERSON>DDA<PERSON> ON}
{$IOCHECKS ON}
{ LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $5052FFF0}
{$DESCRIPTION 'NetMasters Fastnet Tools'}
{$DESIGNONLY}
{$IMPLICITBUILD OFF}

requires
  vcl,
  designide;

contains
  NMReg in 'NMReg.pas';

end.
