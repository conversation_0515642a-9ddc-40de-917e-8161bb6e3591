package DelphiX_for4;

{$R *.RES}
{$R 'DelphiX.dcr'}
{$ALIGN ON}
{$ASSERTIONS OFF}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS OFF}
{$LOCALSYMBOLS OFF}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $00400000}
{$DESCRIPTION 'DelphiX - DirectX components for Delphi'}
{$IMPLICITBUILD ON}

requires
  vcl40,
  VCLSMP40;

contains
  DIB in 'DIB.pas',
  DXClass in 'DXClass.pas',
  DXConsts in 'DXConsts.pas',
  DXDraws in 'DXDraws.pas',
  DXETable in 'DXETable.pas',
  DXInput in 'DXInput.pas',
  DXPlay in 'DXPlay.pas',
  DXPlayFm in 'DXPlayFm.pas',
  DXRender in 'DXRender.pas',
  DXReg in 'DXReg.pas',
  DXSounds in 'DXSounds.pas',
  DXSprite in 'DXSprite.pas',
  Wave in 'Wave.pas',
  DXFFBEdit in 'DXFFBEdit.pas',
  DXGUIDEdit in 'DXGUIDEdit.pas',
  DXInptEdit in 'DXInptEdit.pas',
  DXPictEdit in 'DXPictEdit.pas',
  DXWaveEdit in 'DXWaveEdit.pas',
  DirectX in 'DirectX.pas',
  DShow in 'DShow.pas',
  DAnim in 'DAnim.pas',
  DXTexImg in 'DXTexImg.pas';

end.
