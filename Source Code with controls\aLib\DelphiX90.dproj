﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{6ec6e3ed-f62f-4eba-bd15-b71b48535d8d}</ProjectGuid>
    <MainSource>DelphiX90.dpk</MainSource>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>C:\Documents and Settings\All Users\Documents\RAD Studio\5.0\Bpl\DelphiX90.bpl</DCC_DependencyCheckOutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_AssertionsAtRuntime>False</DCC_AssertionsAtRuntime>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_WriteableConstants>True</DCC_WriteableConstants>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_AssertionsAtRuntime>False</DCC_AssertionsAtRuntime>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_WriteableConstants>True</DCC_WriteableConstants>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType>Package</Borland.ProjectType>
    <BorlandProject>
<BorlandProject><Delphi.Personality><Parameters><Parameters Name="UseLauncher">False</Parameters><Parameters Name="LoadAllSymbols">True</Parameters><Parameters Name="LoadUnspecifiedSymbols">False</Parameters></Parameters><Package_Options><Package_Options Name="PackageDescription">DelphiX - DirectX components for Delphi</Package_Options><Package_Options Name="ImplicitBuild">True</Package_Options><Package_Options Name="DesigntimeOnly">False</Package_Options><Package_Options Name="RuntimeOnly">False</Package_Options></Package_Options><VersionInfo><VersionInfo Name="IncludeVerInfo">True</VersionInfo><VersionInfo Name="AutoIncBuild">False</VersionInfo><VersionInfo Name="MajorVer">1</VersionInfo><VersionInfo Name="MinorVer">0</VersionInfo><VersionInfo Name="Release">0</VersionInfo><VersionInfo Name="Build">0</VersionInfo><VersionInfo Name="Debug">False</VersionInfo><VersionInfo Name="PreRelease">False</VersionInfo><VersionInfo Name="Special">False</VersionInfo><VersionInfo Name="Private">False</VersionInfo><VersionInfo Name="DLL">False</VersionInfo><VersionInfo Name="Locale">1029</VersionInfo><VersionInfo Name="CodePage">1250</VersionInfo></VersionInfo><VersionInfoKeys><VersionInfoKeys Name="CompanyName"></VersionInfoKeys><VersionInfoKeys Name="FileDescription"></VersionInfoKeys><VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys><VersionInfoKeys Name="InternalName"></VersionInfoKeys><VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys><VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys><VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys><VersionInfoKeys Name="ProductName"></VersionInfoKeys><VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys><VersionInfoKeys Name="Comments"></VersionInfoKeys></VersionInfoKeys><Source><Source Name="MainSource">DelphiX90.dpk</Source></Source></Delphi.Personality></BorlandProject></BorlandProject>
  </ProjectExtensions>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
  <ItemGroup>
    <DelphiCompile Include="DelphiX90.dpk">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="Colli3DX.dcr" />
    <DCCReference Include="Colli3DX.dcr" />
    <DCCReference Include="Colli3DX.pas" />
    <DCCReference Include="DAnim.pas" />
    <DCCReference Include="DelphiX.dcr" />
    <DCCReference Include="DelphiX.dcr" />
    <DCCReference Include="designide.dcp" />
    <DCCReference Include="DIB.pas" />
    <DCCReference Include="DirectX.pas" />
    <DCCReference Include="DShow.pas" />
    <DCCReference Include="DXClass.pas" />
    <DCCReference Include="DXCommon.pas" />
    <DCCReference Include="DXConsts.pas" />
    <DCCReference Include="DXDraws.pas" />
    <DCCReference Include="DXETable.pas" />
    <DCCReference Include="DXFFBEdit.pas" />
    <DCCReference Include="DXGUIDEdit.pas" />
    <DCCReference Include="DXInptEdit.pas" />
    <DCCReference Include="DXInput.pas" />
    <DCCReference Include="DXMidiEdit.pas">
      <Form>DelphiXMidiEditForm</Form>
    </DCCReference>
    <DCCReference Include="DXPictEdit.pas" />
    <DCCReference Include="DXPlay.pas" />
    <DCCReference Include="DXPlayFm.pas" />
    <DCCReference Include="DXReg.pas" />
    <DCCReference Include="DXRender.pas" />
    <DCCReference Include="DXSounds.pas" />
    <DCCReference Include="DXSprite.pas" />
    <DCCReference Include="DXSpriteEdit.pas">
      <Form>DelphiXSpriteEditForm</Form>
    </DCCReference>
    <DCCReference Include="DXWave.pas" />
    <DCCReference Include="DXWaveEdit.pas" />
    <DCCReference Include="rtl.dcp" />
    <DCCReference Include="TurboPixels.pas" />
    <DCCReference Include="vcl.dcp" />
    <DCCReference Include="vclsmp.dcp" />
  </ItemGroup>
</Project>