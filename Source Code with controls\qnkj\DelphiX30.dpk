package DelphiX30;

{$R *.RES}
{$R 'DelphiX.dcr'}
{$ALIGN ON}
{$ASSERTIONS OFF}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS OFF}
{$LOCALSYMBOLS OFF}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $00400000}
{$DESCRIPTION 'DelphiX - DirectX component collection for Delphi'}
{$IMPLICITBUILD ON}

requires
  vcl30,
  VCLSMP30;

contains
  D<PERSON>,
  DXClass,
  DXConsts,
  DXDraws,
  DXETable,
  DXInput,
  DXPlay,
  DX<PERSON>layFm,
  DX<PERSON><PERSON>,
  DXReg,
  DXSounds,
  DXSprite,
  DXWave,
  DX<PERSON>BEdit,
  DXGUIDEdit,
  DXInptEdit,
  DXPictEdit,
  DXWaveEdit,
  DAnim,
  DirectX,
  DShow,
  turbopixels,
  DXCommon,
  DXSpriteEdit,
  DXMidiEdit
;

end.